// out: false

@font-face {
  font-family: 'icomoon';
  src:  url('assets/fonts/icomoon.eot?twq3hi');
  src:  url('assets/fonts/icomoon.eot?twq3hi#iefix') format('embedded-opentype'),
    url('assets/fonts/icomoon.ttf?twq3hi') format('truetype'),
    url('assets/fonts/icomoon.woff?twq3hi') format('woff'),
    url('assets/fonts/icomoon.svg?twq3hi#icomoon') format('svg');
  font-weight: normal;
  font-style: normal; 
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-balloon:before {
  content: "\e900";
}
.icon-bed:before {
  content: "\e901";
}
.icon-calendar:before {
  content: "\e902";
}
.icon-checkmark:before {
  content: "\e903";
}
.icon-chevron-down:before {
  content: "\e904";
}
.icon-chevron-left:before {
  content: "\e905";
}
.icon-chevron-right:before {
  content: "\e906";
}
.icon-chevron-up:before {
  content: "\e907";
}
.icon-close:before {
  content: "\e908";
}
.icon-facebook:before {
  content: "\e909";
}
.icon-instagram:before {
  content: "\e90a";
}
.icon-lindenhof:before {
  content: "\e90b";
}
.icon-location:before {
  content: "\e90c";
}
.icon-mail:before {
  content: "\e90d";
}
.icon-person:before {
  content: "\e90e";
}
.icon-phone:before {
  content: "\e90f";
}
.icon-star_half:before {
  content: "\e910";
}
.icon-star:before {
  content: "\e911";
}
.icon-whatsapp:before {
  content: "\e912";
}
.icon-x:before {
  content: "\e913";
}

.hugeTitle, .biggerTitle, .bigTitle, .subTitle, .text, .writtenTitle, .title, .textTitle {
  &.white {
    color: @hardWhite;
  }
  &.secondary {
    color: @secondaryColor;
  }
}
.hugeTitle {
  font-size: @vw100 + @vw40; 
  font-family: "Playfair Display", Arial;
  font-weight: 400;
}

section {
  &.inview {
    [data-letters] {
      visibility: visible;
      .letter {
        .transform(translateY(0));
        opacity: 1;
        transition: opacity 0.6s cubic-bezier(0.76, 0, 0.24, 1), transform 0.6s cubic-bezier(0.76, 0, 0.24, 1);
        -webkit-transition: opacity 0.6s cubic-bezier(0.76, 0, 0.24, 1), transform 0.6s cubic-bezier(0.76, 0, 0.24, 1);
        .stagger(30, 0.015s);
      }
    }
  }
}

[data-letters] {
  visibility: hidden;
  .letter {
    .transform(translateY(@vw10));
    opacity: 0;
  }
}

.biggerTitle {
  font-size: @vw78;
  font-family: "Gotham", Arial;
  font-weight: 1000;
  line-height: .8;
  text-transform: uppercase;
  letter-spacing: -4px;
  strong {
    letter-spacing: -12px;
    font-weight: 1000;
    font-size: @vw100 + @vw100;
  }
}

.bigTitle {
  font-size: @vw42;
  font-family: "Playfair Display", Arial;
  font-weight: 400;
  line-height: 1.2;
}

.title {
  font-size: @vw36;
  font-family: "Playfair Display", Arial;
  font-style: italic;
  font-weight: 300;
  &.bold {
    font-weight: 700;
  }
}

.smallTitle {
  font-size: @vw28;
  font-family: "Inter", Arial;
  font-weight: 300;
  line-height: 1.2;
}

.writtenTitle {
  font-size: @vw50;
  font-family: 'SignPainter';
  font-weight: 400;
  line-height: 2.2;
}

.textTitle {
  font-size: @vw16;
  line-height: 1.4;
  font-family: "Inter", Arial;
  font-weight: 300;
  &.upper {
    font-size: @vw14;
    text-transform: uppercase;
  }
  &.smaller {
    font-size: @vw14;
  }
  &.primary {
    color: @primaryColor;
  }
  &.secondary {
    color: @secondaryColor;
  }
}

.text {
  p {
    line-height: 1.4;
    font-size: @vw18;
    &:not(:last-child) {
      margin-bottom: @vw22;
    }
  }
}

@media all and (max-width: 1160px) {
  .hugeTitle {
    font-size: @vw100-1160 + @vw40-1160; 
  }

  .biggerTitle {
    font-size: @vw78-1160;
  }

  .bigTitle {
    font-size: @vw42-1160;
  }

  .title {
    font-size: @vw50-1160;
  }

  .smallTitle {
    font-size: @vw28-1160;
  }

  .writtenTitle {
    font-size: @vw50-1160;
  }
  
  .subTitle {
    font-size: @vw30-1160;
    margin-bottom: @vw20-1160;
  }

  .textTitle {
    font-size: @vw16-1160;
    &.upper {
      font-size: @vw16-1160;
    }
    &.smaller {
      font-size: @vw16-1160;
    }
  }

  .text {
    &.bigger {
      p {
        font-size: @vw30-1160;
      }
    }
    &:not(:first-child) {
      margin-top: @vw20-1160;
    }

    p {
      &:not(:last-child) {
        margin-bottom: @vw22-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .hugeTitle {
    font-size: @vw100-580 + @vw40-580;
  }

  .biggerTitle {
    letter-spacing: -2px;
    font-size: @vw50-580;
    strong {
      letter-spacing: -5px;
      font-size: @vw120-580;
    }
  }

  .bigTitle {
    font-size: @vw42-580;
  }

  .title {
    font-size: @vw50-580;
  }

  .smallTitle {
    font-size: @vw28-580;
  }

  .writtenTitle {
    font-size: @vw50-580;
  }

  .subTitle {
    font-size: @vw34-580;
    margin-bottom: @vw20-580;
  }

  .textTitle {
    font-size: @vw20-580;
    &.upper {
      font-size: @vw20-580;
    }
    &.smaller {
      font-size: @vw20-580;
    }
  }

  .text {
    &.bigger {
      p {
        font-size: @vw24-580;
      }
    }
    &:not(:first-child) {
      margin-top: @vw20-580;
    }

    p {
      &:not(:last-child) {
        margin-bottom: @vw22-580;
      }
    }
  }
}
