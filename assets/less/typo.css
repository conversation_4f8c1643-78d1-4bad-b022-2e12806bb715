.hugeTitle.white,
.biggerTitle.white,
.bigTitle.white,
.subTitle.white,
.text.white,
.writtenTitle.white,
.title.white,
.textTitle.white {
  color: #FFFFFF;
}
.hugeTitle.secondary,
.biggerTitle.secondary,
.bigTitle.secondary,
.subTitle.secondary,
.text.secondary,
.writtenTitle.secondary,
.title.secondary,
.textTitle.secondary {
  color: #8B4513;
}
.hugeTitle {
  font-size: 8.102vw;
  font-family: "Playfair Display", <PERSON><PERSON>;
  font-weight: 400;
}
section.inview [data-letters] {
  visibility: visible;
}
section.inview [data-letters] .letter {
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -o-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
  opacity: 1;
  transition: opacity 0.6s cubic-bezier(0.76, 0, 0.24, 1), transform 0.6s cubic-bezier(0.76, 0, 0.24, 1);
  -webkit-transition: opacity 0.6s cubic-bezier(0.76, 0, 0.24, 1), transform 0.6s cubic-bezier(0.76, 0, 0.24, 1);
}
section.inview [data-letters] .letter:nth-child(30) {
  transition-delay: 0.45s;
}
section.inview [data-letters] .letter:nth-child(29) {
  transition-delay: 0.435s;
}
section.inview [data-letters] .letter:nth-child(28) {
  transition-delay: 0.42s;
}
section.inview [data-letters] .letter:nth-child(27) {
  transition-delay: 0.405s;
}
section.inview [data-letters] .letter:nth-child(26) {
  transition-delay: 0.39s;
}
section.inview [data-letters] .letter:nth-child(25) {
  transition-delay: 0.375s;
}
section.inview [data-letters] .letter:nth-child(24) {
  transition-delay: 0.36s;
}
section.inview [data-letters] .letter:nth-child(23) {
  transition-delay: 0.345s;
}
section.inview [data-letters] .letter:nth-child(22) {
  transition-delay: 0.33s;
}
section.inview [data-letters] .letter:nth-child(21) {
  transition-delay: 0.315s;
}
section.inview [data-letters] .letter:nth-child(20) {
  transition-delay: 0.3s;
}
section.inview [data-letters] .letter:nth-child(19) {
  transition-delay: 0.285s;
}
section.inview [data-letters] .letter:nth-child(18) {
  transition-delay: 0.27s;
}
section.inview [data-letters] .letter:nth-child(17) {
  transition-delay: 0.255s;
}
section.inview [data-letters] .letter:nth-child(16) {
  transition-delay: 0.24s;
}
section.inview [data-letters] .letter:nth-child(15) {
  transition-delay: 0.225s;
}
section.inview [data-letters] .letter:nth-child(14) {
  transition-delay: 0.21s;
}
section.inview [data-letters] .letter:nth-child(13) {
  transition-delay: 0.195s;
}
section.inview [data-letters] .letter:nth-child(12) {
  transition-delay: 0.18s;
}
section.inview [data-letters] .letter:nth-child(11) {
  transition-delay: 0.165s;
}
section.inview [data-letters] .letter:nth-child(10) {
  transition-delay: 0.15s;
}
section.inview [data-letters] .letter:nth-child(9) {
  transition-delay: 0.135s;
}
section.inview [data-letters] .letter:nth-child(8) {
  transition-delay: 0.12s;
}
section.inview [data-letters] .letter:nth-child(7) {
  transition-delay: 0.105s;
}
section.inview [data-letters] .letter:nth-child(6) {
  transition-delay: 0.09s;
}
section.inview [data-letters] .letter:nth-child(5) {
  transition-delay: 0.075s;
}
section.inview [data-letters] .letter:nth-child(4) {
  transition-delay: 0.06s;
}
section.inview [data-letters] .letter:nth-child(3) {
  transition-delay: 0.045s;
}
section.inview [data-letters] .letter:nth-child(2) {
  transition-delay: 0.03s;
}
section.inview [data-letters] .letter:nth-child(1) {
  transition-delay: 0.015s;
}
[data-letters] {
  visibility: hidden;
}
[data-letters] .letter {
  -webkit-transform: translateY(0.579vw);
  -moz-transform: translateY(0.579vw);
  -o-transform: translateY(0.579vw);
  -ms-transform: translateY(0.579vw);
  transform: translateY(0.579vw);
  opacity: 0;
}
.biggerTitle {
  font-size: 4.514vw;
  font-family: "Gotham", Arial;
  font-weight: 1000;
  line-height: 0.8;
  text-transform: uppercase;
  letter-spacing: -4px;
}
.biggerTitle strong {
  letter-spacing: -12px;
  font-weight: 1000;
  font-size: 11.574vw;
}
.bigTitle {
  font-size: 2.431vw;
  font-family: "Playfair Display", Arial;
  font-weight: 400;
  line-height: 1.2;
}
.title {
  font-size: 2.083vw;
  font-family: "Playfair Display", Arial;
  font-style: italic;
  font-weight: 300;
}
.title.bold {
  font-weight: 700;
}
.smallTitle {
  font-size: 1.62vw;
  font-family: "Inter", Arial;
  font-weight: 300;
  line-height: 1.2;
}
.writtenTitle {
  font-size: 2.894vw;
  font-family: 'SignPainter';
  font-weight: 400;
  line-height: 2.2;
}
.textTitle {
  font-size: 0.926vw;
  line-height: 1.4;
  font-family: "Inter", Arial;
  font-weight: 300;
}
.textTitle.upper {
  font-size: 0.81vw;
  text-transform: uppercase;
}
.textTitle.smaller {
  font-size: 0.81vw;
}
.textTitle.primary {
  color: #2E5A3E;
}
.textTitle.secondary {
  color: #8B4513;
}
.text p {
  line-height: 1.4;
  font-size: 1.042vw;
}
.text p:not(:last-child) {
  margin-bottom: 1.273vw;
}
@media all and (max-width: 1160px) {
  .hugeTitle {
    font-size: 12.069vw;
  }
  .biggerTitle {
    font-size: 6.724vw;
  }
  .bigTitle {
    font-size: 3.621vw;
  }
  .title {
    font-size: 4.31vw;
  }
  .smallTitle {
    font-size: 2.414vw;
  }
  .writtenTitle {
    font-size: 4.31vw;
  }
  .subTitle {
    font-size: 2.586vw;
    margin-bottom: 1.724vw;
  }
  .textTitle {
    font-size: 1.379vw;
  }
  .textTitle.upper {
    font-size: 1.379vw;
  }
  .textTitle.smaller {
    font-size: 1.379vw;
  }
  .text.bigger p {
    font-size: 2.586vw;
  }
  .text:not(:first-child) {
    margin-top: 1.724vw;
  }
  .text p:not(:last-child) {
    margin-bottom: 1.897vw;
  }
}
@media all and (max-width: 580px) {
  .hugeTitle {
    font-size: 24.138vw;
  }
  .biggerTitle {
    letter-spacing: -2px;
    font-size: 8.62vw;
  }
  .biggerTitle strong {
    letter-spacing: -5px;
    font-size: 20.69vw;
  }
  .bigTitle {
    font-size: 7.241vw;
  }
  .title {
    font-size: 8.62vw;
  }
  .smallTitle {
    font-size: 4.827vw;
  }
  .writtenTitle {
    font-size: 8.62vw;
  }
  .subTitle {
    font-size: 5.862vw;
    margin-bottom: 3.448vw;
  }
  .textTitle {
    font-size: 3.448vw;
  }
  .textTitle.upper {
    font-size: 3.448vw;
  }
  .textTitle.smaller {
    font-size: 3.448vw;
  }
  .text.bigger p {
    font-size: 4.137vw;
  }
  .text:not(:first-child) {
    margin-top: 3.448vw;
  }
  .text p:not(:last-child) {
    margin-bottom: 3.793vw;
  }
}
