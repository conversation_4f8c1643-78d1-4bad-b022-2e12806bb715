// out: false
.introBlock {
    .backgroundGallery {
        width: 100%;
        height: @vw100 * 6;
        position: relative;
        display: block;
        .innerCol {
            display: inline-block;
            height: 100%;
            vertical-align: top;
            &:nth-child(1) {
                width: calc(50% ~"+" @vw106 ~"+" @vw16);
                .galleryItem {
                    height: 100%;
                }
            }
            &:nth-child(2) {
                padding-left: @vw16;
                width: calc(50% ~"- (" @vw106 ~"+" @vw16 ~")");
                display: inline-flex;
                flex-direction: column;
                justify-content: space-between;
                .galleryItem {
                    height: 50%;
                    &:first-child {
                        margin-bottom: @vw16;
                    }
                }
            }
        }
        .galleryItem {
            .rounded(@vw5);
            overflow: hidden;
            position: relative;
            width: 100%;
            display: inline-block;
            img {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
            }
        }
    }
    .headerWrapper {
        .title {
            margin: @vw14 0 @vw22 0;
        }
    }
    .cols {
        padding-right: (@vw106 + @vw16);
        .transform(translateY(-@vw60));
        &:before {
            content: '';
            position: absolute;
            top: -@vw40;
            right: @vw106;
            width: 70%;
            height: @vw100 * 2;
            background: @almostWhite;
            opacity: 1;
            pointer-events: none;
            .rounded(@vw5);
        }
        .col {
            display: inline-block;
            vertical-align: top;
            &:nth-child(1) {
                width: calc(100% ~"- (" @vw106 ~"*" 3 ~"+" @vw16 ~"*" 3 ~")");
                .innerCol {
                    display: inline-block;
                    vertical-align: top;
                    &:first-child {
                        width: (@vw106 * 3) + (@vw16 * 3);
                    }
                    &:nth-child(2) {
                        width: calc(100% ~"- (" @vw106 ~"*" 3 ~"+" @vw16 ~"*" 3 ~")");
                    }
                }
            }
            &:nth-child(2) {
                width: (@vw106 * 3) + (@vw16 * 3);
            }
        }
    }
    .divider {
        margin: @vw100 + @vw40 0;
        border: 1px dashed @lightGrey;
    }
}