<?php
/**
 * Intro Block Template
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get ACF fields
$title = get_field('title');
$subtitle = get_field('subtitle');
$content = get_field('content');
$background_image = get_field('background_image');
$gallery = get_field('gallery');

// Google Reviews fields
$show_google_reviews = get_field('show_google_reviews');
$google_place_id = get_field('google_place_id');

// Content sections fields
$show_faciliteiten = get_field('show_faciliteiten');
$show_eten_drinken = get_field('show_eten_drinken');
$show_omgeving = get_field('show_omgeving');
$show_algemeen = get_field('show_algemeen');

// Booking widget fields
$show_sticky_booking = get_field('show_sticky_booking');
$booking_widget_type = get_field('booking_widget_type');
$hotelhuurder_embed = get_field('hotelhuurder_embed');

// Get accommodatie info from settings
$accommodatie_locatie = lindenhof_get_setting('accommodatie_locatie');
$accommodatie_personen = lindenhof_get_setting('accommodatie_personen');
$accommodatie_slaapplaatsen = lindenhof_get_setting('accommodatie_slaapplaatsen');

// Get Google Reviews data if needed
$reviews_data = null;
if ($show_google_reviews && $google_place_id) {
    $reviews_data = lindenhof_get_google_reviews($google_place_id);
    // Check if it's a WP_Error and set to null if so
    if (is_wp_error($reviews_data)) {
        $reviews_data = null;
    }
}

// Generate unique ID for this block instance
$block_id = $block_id ?? lindenhof_unique_id('introBlock');

// Build CSS classes
$css_classes = lindenhof_bem_classes('introBlock', '', '', array('lindenhof-block'));

// Build inline styles for background image
$inline_styles = '';
if ($background_image && !empty($background_image['url'])) {
    $inline_styles = sprintf(
        'style="background-image: url(%s);"',
        esc_url($background_image['url'])
    );
}
?>

<section class="introBlock" data-anchor="home" data-init>
    <div class="contentWrapper">
        <?php if ($gallery && is_array($gallery)): ?>
            <div class="backgroundGallery">
                 <?php $gallery = array_slice($gallery, 0, 3); ?>
                  <?php $gallery = array_reverse($gallery); ?>
                <?php foreach ($gallery as $image): ?>
                     <?php if ($image == $gallery[0]): ?>
                        <div class="innerCol">
                            <div class="galleryItem" data-gallery data-image="<?php echo esc_url($image['url']); ?>">
                                <?php echo lindenhof_safe_image($image, 'large', lindenhof_bem_classes('intro-block', 'gallery-image')); ?>
                            </div>
                        </div>
                        <div class="innerCol">
                    <?php else: ?>
                            <div class="galleryItem" data-gallery data-image="<?php echo esc_url($image['url']); ?>">
                                <?php echo lindenhof_safe_image($image, 'medium_large', lindenhof_bem_classes('intro-block', 'gallery-image')); ?>
                            </div>
                    <?php endif; ?>
                    
                <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
        <div class="cols">
        <div class="col">
            <div class="innerCol">
                <div class="logo">
                     <img src="<?php echo get_stylesheet_directory_uri(); ?>/logo.png" alt="Logo | <?php echo get_bloginfo('name'); ?>">
                </div>
            </div>
        <?php if ($title): ?>
            <div class="innerCol">
                <div class="headerWrapper">
                    <div class="headerInfo">
                        <?php if ($show_google_reviews && $google_place_id && $reviews_data && isset($reviews_data['rating'])): ?>
                            <div class="googleReviews">
                                <?php echo lindenhof_render_google_stars($reviews_data['rating']); ?>
                                <span class="rating">
                                    <?php echo esc_html($reviews_data['rating']); ?>
                                </span>
                                <span class="totalRatings">
                                    (<?php echo esc_html($reviews_data['total_ratings'] ?? 0); ?>)
                                </span>
                            </div>
                        <?php endif; ?>
                            <div class="specs">
                        <?php if ($accommodatie_locatie): ?>
                            <div class="spec">
                                icon
                                <span>
                                    <?php echo esc_html($accommodatie_locatie); ?>
                                </span>
                            </div>
                        <?php endif; ?>

                        <?php if ($accommodatie_personen): ?>
                            <div class="spec">
                                icon
                                <span>
                                    <?php echo sprintf(__('%d personen', 'lindenhof'), $accommodatie_personen); ?>
                                </span>
                            </div>
                        <?php endif; ?>

                        <?php if ($accommodatie_slaapplaatsen): ?>
                            <div class="spec">
                                icon
                                <span>
                                    <?php echo sprintf(__('%d slaapplaatsen', 'lindenhof'), $accommodatie_slaapplaatsen); ?>
                                </span>
                            </div>
                        <?php endif; ?>
                        </div>
                    </div>

                    <h1 class="title bold" data-lines data-letters>
                        <?php echo lindenhof_safe_text($title); ?>
                    </h1>

                    <div class="text">
                        <?php echo html_entity_decode($content); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="divider"></div>
        <?php
        // Content sections
        $content_sections = array();

        if ($show_faciliteiten) {
            $faciliteiten_data = lindenhof_get_block_content('faciliteiten-block');
            if ($faciliteiten_data) {
                $content_sections['faciliteiten'] = array(
                    'title' => $faciliteiten_data['title'] ?? __('Faciliteiten', 'lindenhof'),
                    'type' => 'faciliteiten',
                    'data' => $faciliteiten_data
                );
            }
        }

        if ($show_eten_drinken) {
            $eten_drinken_data = lindenhof_get_block_content('eten-drinken-block');
            if ($eten_drinken_data) {
                $content_sections['eten_drinken'] = array(
                    'title' => $eten_drinken_data['title'] ?? __('Eten & Drinken', 'lindenhof'),
                    'type' => 'eten_drinken',
                    'data' => $eten_drinken_data
                );
            }
        }

        if ($show_omgeving) {
            $omgeving_data = lindenhof_get_block_content('omgeving-block');
            if ($omgeving_data) {
                $content_sections['omgeving'] = array(
                    'title' => $omgeving_data['title'] ?? __('Omgeving', 'lindenhof'),
                    'type' => 'omgeving',
                    'data' => $omgeving_data
                );
            }
        }

        if ($show_algemeen) {
            $algemeen_data = lindenhof_get_block_content('algemeen-block');
            if ($algemeen_data) {
                $content_sections['algemeen'] = array(
                    'title' => $algemeen_data['title'] ?? __('Algemeen', 'lindenhof'),
                    'type' => 'algemeen',
                    'data' => $algemeen_data
                );
            }
        }
        ?>

        faciliteiten:

        <?php if (!empty($content_sections)): ?>
            <div class="<?php echo lindenhof_bem_classes('intro-block', 'content-sections'); ?>">
                <div class="<?php echo lindenhof_bem_classes('intro-block', 'sections-grid'); ?>">
                    <?php foreach ($content_sections as $section_key => $section): ?>
                        <div class="<?php echo lindenhof_bem_classes('intro-block', 'section'); ?> <?php echo lindenhof_bem_classes('intro-block', 'section', $section['type']); ?>">

                            <h3 class="<?php echo lindenhof_bem_classes('intro-block', 'section-title'); ?>">
                                <?php echo lindenhof_safe_text($section['title']); ?>
                            </h3>

                            <?php if ($section['type'] === 'faciliteiten' && isset($section['data']['facilities'])): ?>
                                <div class="<?php echo lindenhof_bem_classes('intro-block', 'facilities-preview'); ?>">
                                    <?php
                                    $facilities = array_slice($section['data']['facilities'], 0, 3); // Show first 3
                                    foreach ($facilities as $facility):
                                    ?>
                                        <div class="<?php echo lindenhof_bem_classes('intro-block', 'facility-item'); ?>">
                                            <?php if (isset($facility['icon'])): ?>
                                                <span class="<?php echo lindenhof_bem_classes('intro-block', 'facility-icon'); ?>">
                                                    <?php echo lindenhof_safe_text($facility['icon']); ?>
                                                </span>
                                            <?php endif; ?>
                                            <span class="<?php echo lindenhof_bem_classes('intro-block', 'facility-title'); ?>">
                                                <?php echo lindenhof_safe_text($facility['title'] ?? ''); ?>
                                            </span>
                                        </div>
                                    <?php endforeach; ?>
                                    <?php if (count($section['data']['facilities']) > 3): ?>
                                        <p class="<?php echo lindenhof_bem_classes('intro-block', 'more-info'); ?>">
                                            <?php echo sprintf(__('En nog %d meer...', 'lindenhof'), count($section['data']['facilities']) - 3); ?>
                                        </p>
                                    <?php endif; ?>
                                </div>

                            <?php elseif ($section['type'] === 'eten_drinken' && isset($section['data']['content'])): ?>
                                <div class="<?php echo lindenhof_bem_classes('intro-block', 'content-preview'); ?>">
                                    <?php echo wp_trim_words(lindenhof_safe_text($section['data']['content'], true), 20, '...'); ?>
                                </div>

                            <?php elseif ($section['type'] === 'omgeving' && isset($section['data']['content'])): ?>
                                <div class="<?php echo lindenhof_bem_classes('intro-block', 'content-preview'); ?>">
                                    <?php echo wp_trim_words(lindenhof_safe_text($section['data']['content'], true), 20, '...'); ?>
                                </div>

                            <?php elseif ($section['type'] === 'algemeen' && isset($section['data']['content'])): ?>
                                <div class="<?php echo lindenhof_bem_classes('intro-block', 'content-preview'); ?>">
                                    <?php echo wp_trim_words(lindenhof_safe_text($section['data']['content'], true), 20, '...'); ?>
                                </div>

                            <?php endif; ?>

                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
    <div class="col">
    <?php if ($show_sticky_booking): ?>
        <div class="stickyBooking" data-widget-type="<?php echo esc_attr($booking_widget_type); ?>">
            <?php if ($booking_widget_type === 'hotelhuurder' && $hotelhuurder_embed): ?>
                <div class="bookingWidget">
                    <?php echo $hotelhuurder_embed; // Already sanitized by ACF ?>
                </div>

            <?php elseif ($booking_widget_type === 'huurkalender'): ?>
                <div class="<?php echo lindenhof_bem_classes('intro-block', 'booking-widget'); ?>">
                    <div class="<?php echo lindenhof_bem_classes('intro-block', 'booking-header'); ?>">
                        <h4 class="<?php echo lindenhof_bem_classes('intro-block', 'booking-title'); ?>">
                            <?php echo esc_html__('Direct boeken', 'lindenhof'); ?>
                        </h4>
                        <button class="<?php echo lindenhof_bem_classes('intro-block', 'booking-close'); ?>" aria-label="<?php echo esc_attr__('Sluiten', 'lindenhof'); ?>">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="<?php echo lindenhof_bem_classes('intro-block', 'booking-content'); ?>">
                        <!-- Huurkalender.nl Direct Booking Embed -->
                        <link href="https://www.huurkalender.nl/online/embed/huurkalender.css" rel="stylesheet" />
                        <div class="huurkalender-embed huurkalender-embed-container_booking">
                            <iframe
                                id="iframe_huurkalender_booking_sticky_<?php echo esc_attr($block_id); ?>"
                                src="https://www.huurkalender.nl/vacancy/booking/lindenhof-geijsteren-21494.html?type=iframe&lang=nl"
                                frameborder="0"
                                width="100%"
                                allowfullscreen
                                style="min-height: 400px;"
                            ></iframe>
                            <script type="text/javascript" src="https://www.huurkalender.nl/online/embed/huurkalender.js"></script>
                        </div>
                    </div>
                </div>

            <?php endif; ?>

            <button class="<?php echo lindenhof_bem_classes('intro-block', 'booking-toggle'); ?>" aria-label="<?php echo esc_attr__('Booking widget openen', 'lindenhof'); ?>">
                <span class="<?php echo lindenhof_bem_classes('intro-block', 'booking-icon'); ?>">📅</span>
                <span class="<?php echo lindenhof_bem_classes('intro-block', 'booking-text'); ?>">
                    <?php echo esc_html__('Boek nu', 'lindenhof'); ?>
                </span>
            </button>

        </div>
    <?php endif; ?>
        </div>
    </div>
</section>
